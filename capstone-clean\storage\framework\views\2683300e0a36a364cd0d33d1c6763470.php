<?php $__env->startSection('title', 'Student Feedback - Cook Dashboard'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0"><i class="bi bi-chat-dots me-2"></i>Student Feedback</h3>
                        <p class="mb-0 text-muted">View and analyze student feedback on meals</p>
                    </div>
                    <div class="text-end">
                        <span class="badge bg-primary fs-6 me-3"><?php echo e($stats['total_feedback']); ?> Total Reviews</span>
                        <?php if($stats['total_feedback'] > 0): ?>
                            <button type="button" class="btn btn-outline-danger btn-sm" onclick="confirmDeleteAll()">
                                <i class="bi bi-trash me-1"></i> Delete All Feedback
                            </button>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-2">
            <div class="card text-center">
                <div class="card-body">
                    <h2 class="text-primary"><?php echo e($stats['average_rating']); ?></h2>
                    <p class="mb-0">Average Rating</p>
                    <div class="mt-2">
                        <?php for($i = 1; $i <= 5; $i++): ?>
                            <i class="bi <?php echo e($i <= $stats['average_rating'] ? 'bi-star-fill text-warning' : 'bi-star'); ?>"></i>
                        <?php endfor; ?>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card text-center">
                <div class="card-body">
                    <h2 class="text-success"><?php echo e($stats['total_feedback']); ?></h2>
                    <p class="mb-0">Total Feedback</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card text-center">
                <div class="card-body">
                    <h2 class="text-info"><?php echo e($stats['recent_feedback']); ?></h2>
                    <p class="mb-0">This Week</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card text-center">
                <div class="card-body">
                    <h2 class="text-warning"><?php echo e($stats['rating_distribution'][5]); ?></h2>
                    <p class="mb-0">5-Star Reviews</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card text-center">
                <div class="card-body">
                    <h2 class="text-secondary"><?php echo e($stats['anonymous_feedback']); ?></h2>
                    <p class="mb-0">Anonymous</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card text-center">
                <div class="card-body">
                    <h2 class="text-dark"><?php echo e($stats['identified_feedback']); ?></h2>
                    <p class="mb-0">Identified</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Meal Type Statistics -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Meal Type Performance</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="text-center p-3 border rounded">
                                <h4 class="text-warning"><?php echo e($stats['meal_type_stats']['breakfast']['avg_rating'] ?? 'N/A'); ?></h4>
                                <p class="mb-1"><strong>Breakfast</strong></p>
                                <small class="text-muted"><?php echo e($stats['meal_type_stats']['breakfast']['count']); ?> reviews</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center p-3 border rounded">
                                <h4 class="text-warning"><?php echo e($stats['meal_type_stats']['lunch']['avg_rating'] ?? 'N/A'); ?></h4>
                                <p class="mb-1"><strong>Lunch</strong></p>
                                <small class="text-muted"><?php echo e($stats['meal_type_stats']['lunch']['count']); ?> reviews</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center p-3 border rounded">
                                <h4 class="text-warning"><?php echo e($stats['meal_type_stats']['dinner']['avg_rating'] ?? 'N/A'); ?></h4>
                                <p class="mb-1"><strong>Dinner</strong></p>
                                <small class="text-muted"><?php echo e($stats['meal_type_stats']['dinner']['count']); ?> reviews</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-funnel me-2"></i>Filter & Search Feedback</h5>
                </div>
                <div class="card-body">
                    <form method="GET" action="<?php echo e(route('cook.feedback')); ?>">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="search" class="form-label">Search in Comments & Suggestions</label>
                                <input type="text" class="form-control" id="search" name="search" value="<?php echo e(request('search')); ?>" placeholder="Search for keywords...">
                            </div>
                            <div class="col-md-3">
                                <label for="anonymous_filter" class="form-label">Student Identity</label>
                                <select class="form-control" id="anonymous_filter" name="anonymous_filter">
                                    <option value="">All Feedback</option>
                                    <option value="identified" <?php echo e(request('anonymous_filter') == 'identified' ? 'selected' : ''); ?>>Identified Students</option>
                                    <option value="anonymous" <?php echo e(request('anonymous_filter') == 'anonymous' ? 'selected' : ''); ?>>Anonymous Students</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="rating" class="form-label">Rating</label>
                                <select class="form-control" id="rating" name="rating">
                                    <option value="">All Ratings</option>
                                    <option value="5" <?php echo e(request('rating') == '5' ? 'selected' : ''); ?>>⭐⭐⭐⭐⭐ (5 Stars)</option>
                                    <option value="4" <?php echo e(request('rating') == '4' ? 'selected' : ''); ?>>⭐⭐⭐⭐ (4 Stars)</option>
                                    <option value="3" <?php echo e(request('rating') == '3' ? 'selected' : ''); ?>>⭐⭐⭐ (3 Stars)</option>
                                    <option value="2" <?php echo e(request('rating') == '2' ? 'selected' : ''); ?>>⭐⭐ (2 Stars)</option>
                                    <option value="1" <?php echo e(request('rating') == '1' ? 'selected' : ''); ?>>⭐ (1 Star)</option>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3">
                                <label for="date_from" class="form-label">From Date</label>
                                <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo e(request('date_from')); ?>">
                            </div>
                            <div class="col-md-3">
                                <label for="date_to" class="form-label">To Date</label>
                                <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo e(request('date_to')); ?>">
                            </div>
                            <div class="col-md-3">
                                <label for="meal_type" class="form-label">Meal Type</label>
                                <select class="form-control" id="meal_type" name="meal_type">
                                    <option value="">All Meals</option>
                                    <option value="breakfast" <?php echo e(request('meal_type') == 'breakfast' ? 'selected' : ''); ?>>🌅 Breakfast</option>
                                    <option value="lunch" <?php echo e(request('meal_type') == 'lunch' ? 'selected' : ''); ?>>🌞 Lunch</option>
                                    <option value="dinner" <?php echo e(request('meal_type') == 'dinner' ? 'selected' : ''); ?>>🌙 Dinner</option>
                                </select>
                            </div>
                            <div class="col-md-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="bi bi-search me-1"></i>Filter
                                </button>
                                <a href="<?php echo e(route('cook.feedback')); ?>" class="btn btn-outline-secondary">
                                    <i class="bi bi-arrow-clockwise me-1"></i>Clear
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Feedback List -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Recent Feedback</h5>
                </div>
                <div class="card-body p-0">
                    <?php $__empty_1 = true; $__currentLoopData = $feedbacks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feedback): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <div class="border-bottom p-3 <?php echo e($feedback->rating <= 2 ? 'bg-light-danger' : ''); ?> feedback-item"
                             data-feedback-created="<?php echo e($feedback->created_at->toISOString()); ?>"
                             data-feedback-id="<?php echo e($feedback->id); ?>"
                             data-rating="<?php echo e($feedback->rating); ?>">
                            <div class="row">
                                <div class="col-md-9">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <div>
                                            <h6 class="mb-1">
                                                <span class="badge bg-primary me-2"><?php echo e(ucfirst($feedback->meal_type)); ?></span>
                                                <?php echo e($feedback->meal_date->format('M d, Y')); ?>

                                                <?php if($feedback->is_anonymous): ?>
                                                    <span class="badge bg-secondary ms-2">
                                                        <i class="bi bi-incognito"></i> Anonymous
                                                    </span>
                                                <?php endif; ?>
                                            </h6>
                                        </div>
                                        <div class="rating">
                                            <?php for($i = 1; $i <= 5; $i++): ?>
                                                <i class="bi <?php echo e($i <= $feedback->rating ? 'bi-star-fill text-warning' : 'bi-star'); ?>"></i>
                                            <?php endfor; ?>
                                            <span class="ms-2 fw-bold"><?php echo e($feedback->rating); ?>/5</span>
                                        </div>
                                    </div>

                                    <?php if($feedback->comments): ?>
                                        <div class="mb-2">
                                            <strong class="text-primary">💬 Comments:</strong>
                                            <p class="mb-0 ms-3"><?php echo e($feedback->comments); ?></p>
                                        </div>
                                    <?php endif; ?>

                                    <?php if($feedback->suggestions): ?>
                                        <div class="mb-2">
                                            <strong class="text-info">💡 Suggestions:</strong>
                                            <p class="mb-0 ms-3 text-info"><?php echo e($feedback->suggestions); ?></p>
                                        </div>
                                    <?php endif; ?>

                                    <div class="d-flex justify-content-between align-items-center mt-3">
                                        <small class="text-muted">
                                            <i class="bi bi-person-circle me-1"></i>
                                            <?php if($feedback->is_anonymous): ?>
                                                Anonymous Student
                                            <?php else: ?>
                                                <?php echo e($feedback->student->name ?? 'Student'); ?>

                                            <?php endif; ?>
                                        </small>
                                        <small class="text-muted">
                                            <i class="bi bi-clock me-1"></i>
                                            <?php echo e($feedback->created_at->format('M d, Y \a\t g:i A')); ?>

                                        </small>
                                    </div>
                                </div>
                                <div class="col-md-3 text-end">
                                    <div class="mb-2">
                                        <span class="badge bg-<?php echo e($feedback->rating >= 4 ? 'success' : ($feedback->rating >= 3 ? 'warning' : 'danger')); ?> fs-6">
                                            <?php echo e($feedback->rating); ?>/5 Stars
                                        </span>
                                    </div>
                                    <?php if($feedback->rating <= 2): ?>
                                        <div class="alert alert-warning p-2 mb-2">
                                            <small><i class="bi bi-exclamation-triangle me-1"></i>Needs Attention</small>
                                        </div>
                                    <?php elseif($feedback->rating >= 5): ?>
                                        <div class="alert alert-success p-2 mb-2">
                                            <small><i class="bi bi-star me-1"></i>Excellent!</small>
                                        </div>
                                    <?php endif; ?>

                                    <!-- Delete Button -->
                                    <div class="mt-2">
                                        <button type="button" class="btn btn-outline-danger btn-sm"
                                                onclick="confirmDelete(<?php echo e($feedback->id); ?>, '<?php echo e($feedback->meal_type); ?>', '<?php echo e($feedback->meal_date->format('M d, Y')); ?>')"
                                                title="Delete this feedback">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <div class="p-4 text-center">
                            <i class="bi bi-chat-dots fs-1 text-muted"></i>
                            <p class="mb-0 mt-2">No feedback found matching your criteria.</p>
                            <small class="text-muted">Try adjusting your filters or check back later.</small>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Pagination -->
            <?php if($feedbacks->hasPages()): ?>
                <div class="d-flex justify-content-center mt-4">
                    <?php echo e($feedbacks->links()); ?>

                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
// Confirm delete single feedback
function confirmDelete(feedbackId, mealType, mealDate) {
    if (confirm(`Are you sure you want to delete this feedback?\n\nMeal: ${mealType} on ${mealDate}\n\nThis action cannot be undone.`)) {
        deleteFeedback(feedbackId);
    }
}

// Confirm delete all feedback
function confirmDeleteAll() {
    const totalFeedback = <?php echo e($stats['total_feedback']); ?>;
    if (confirm(`Are you sure you want to delete ALL ${totalFeedback} feedback records?\n\nThis will permanently remove all student feedback from the system.\n\nThis action cannot be undone.`)) {
        if (confirm('This is your final warning!\n\nDeleting all feedback will remove valuable student input data.\n\nAre you absolutely sure?')) {
            deleteAllFeedback();
        }
    }
}

// Delete single feedback via AJAX
function deleteFeedback(feedbackId) {
    fetch(`/cook/feedback/${feedbackId}`, {
        method: 'DELETE',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Remove the feedback item from the page
            const feedbackElement = document.querySelector(`[data-feedback-id="${feedbackId}"]`);
            if (feedbackElement) {
                feedbackElement.style.transition = 'opacity 0.3s ease';
                feedbackElement.style.opacity = '0';
                setTimeout(() => {
                    feedbackElement.remove();
                    // Update the total count
                    updateFeedbackCount(-1);
                }, 300);
            }

            // Show success message
            showAlert('success', 'Feedback deleted successfully');
        } else {
            showAlert('error', data.message || 'Failed to delete feedback');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('error', 'An error occurred while deleting feedback');
    });
}

// Delete all feedback via AJAX
function deleteAllFeedback() {
    fetch('/cook/feedback', {
        method: 'DELETE',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Reload the page to show empty state
            window.location.reload();
        } else {
            showAlert('error', data.message || 'Failed to delete all feedback');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('error', 'An error occurred while deleting all feedback');
    });
}

// Update feedback count in header
function updateFeedbackCount(change) {
    const countBadge = document.querySelector('.badge.bg-primary.fs-6');
    if (countBadge) {
        const currentText = countBadge.textContent;
        const currentCount = parseInt(currentText.match(/\d+/)[0]);
        const newCount = currentCount + change;
        countBadge.textContent = `${newCount} Total Reviews`;

        // Hide delete all button if no feedback left
        if (newCount === 0) {
            const deleteAllBtn = document.querySelector('button[onclick="confirmDeleteAll()"]');
            if (deleteAllBtn) {
                deleteAllBtn.style.display = 'none';
            }
        }
    }
}

// Show alert messages
function showAlert(type, message) {
    // Create alert element
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        <i class="bi bi-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Insert at the top of the container
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>
<?php $__env->stopPush(); ?>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\capstone\capstone-clean\resources\views/cook/feedback/index.blade.php ENDPATH**/ ?>